#!/bin/bash

# 简化版 Qwen Go服务启动脚本

echo "=== 启动 Qwen Go 服务 ==="

# 进入脚本目录
cd "$(dirname "$0")"

# 检查文件
if [ ! -f "qwen" ]; then
    echo "错误: 找不到 qwen 可执行文件"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "错误: 找不到 .env 配置文件"
    exit 1
fi

# 添加执行权限
chmod +x qwen

# 加载环境变量并启动
echo "加载环境变量..."
set -a
source .env
set +a

echo "启动服务..."
echo "服务地址: http://localhost:8080"
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务
./qwen
