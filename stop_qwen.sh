#!/bin/bash

# Qwen Go服务停止脚本

echo "=== 停止 Qwen Go 服务 ==="

# 查找并停止 qwen 进程
PIDS=$(pgrep -f "./qwen")

if [ -z "$PIDS" ]; then
    echo "没有找到运行中的 qwen 服务"
    exit 0
fi

echo "找到运行中的 qwen 进程: $PIDS"

# 优雅停止
echo "正在停止服务..."
for PID in $PIDS; do
    echo "停止进程 $PID"
    kill "$PID"
done

# 等待进程停止
sleep 2

# 检查是否还有进程运行
REMAINING=$(pgrep -f "./qwen")
if [ -n "$REMAINING" ]; then
    echo "强制停止剩余进程: $REMAINING"
    for PID in $REMAINING; do
        kill -9 "$PID"
    done
fi

echo "Qwen Go 服务已停止"

# 检查端口占用
if lsof -i :8080 >/dev/null 2>&1; then
    echo "警告: 端口 8080 仍被占用"
    lsof -i :8080
else
    echo "端口 8080 已释放"
fi
