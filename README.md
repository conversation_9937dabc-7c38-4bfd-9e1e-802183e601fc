# Qwen Go 服务启动指南

## 文件说明

- `qwen` - Go 可执行文件
- `.env` - 环境配置文件
- `solvezs.sql` - 数据库初始化文件
- `start_qwen.sh` - 完整版启动脚本（包含检查）
- `start_simple.sh` - 简化版启动脚本
- `stop_qwen.sh` - 停止服务脚本

## 快速启动

### 方法1: 使用简化启动脚本（推荐）
```bash
./start_simple.sh
```

### 方法2: 使用完整启动脚本
```bash
./start_qwen.sh
```

### 方法3: 手动启动
```bash
# 加载环境变量
set -a && source .env && set +a

# 启动服务
./qwen
```

## 停止服务

```bash
./stop_qwen.sh
```

或者按 `Ctrl+C` 停止正在运行的服务。

## 服务信息

- **服务地址**: http://localhost:8080
- **数据库**: MySQL (localhost:3306)
- **缓存**: Redis (localhost:6379)

## 环境配置

当前 `.env` 配置：
- MySQL: localhost:3306, 数据库: solve, 用户: solve_user
- Redis: localhost:6379, 无密码
- Qwen API: 已配置密钥

## 故障排除

### 1. 权限问题
```bash
chmod +x qwen
chmod +x *.sh
```

### 2. 端口被占用
```bash
# 查看端口占用
lsof -i :8080

# 停止占用进程
./stop_qwen.sh
```

### 3. 数据库连接问题
```bash
# 检查 MySQL 服务
sudo systemctl status mysql
sudo systemctl start mysql

# 测试数据库连接
mysql -u solve_user -p solve
```

### 4. Redis 连接问题
```bash
# 检查 Redis 服务
sudo systemctl status redis-server
sudo systemctl start redis-server

# 测试 Redis 连接
redis-cli ping
```

## API 测试

服务启动后，可以测试以下端点：

```bash
# 基本连接测试
curl http://localhost:8080/

# 常见 API 端点
curl http://localhost:8080/api
curl http://localhost:8080/health
curl http://localhost:8080/ping
```

## 日志查看

服务启动后，日志会直接显示在终端中。如需后台运行：

```bash
# 后台运行并保存日志
nohup ./start_simple.sh > qwen.log 2>&1 &

# 查看日志
tail -f qwen.log
```
