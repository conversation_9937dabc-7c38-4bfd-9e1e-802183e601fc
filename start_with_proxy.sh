#!/bin/bash

# 启动Go服务并通过mitmproxy代理抓包

echo "=== 启动Go服务（通过代理抓包） ==="

# 进入脚本目录
cd "$(dirname "$0")"

# 设置代理环境变量
echo "设置代理环境变量..."
export HTTP_PROXY=http://localhost:8888
export HTTPS_PROXY=http://localhost:8888
export http_proxy=http://localhost:8888
export https_proxy=http://localhost:8888

# 设置SSL证书路径
export SSL_CERT_FILE=/usr/local/share/ca-certificates/mitmproxy.crt
export SSL_CERT_DIR=/etc/ssl/certs

# 显示配置信息
echo "代理配置:"
echo "  HTTP_PROXY: $HTTP_PROXY"
echo "  HTTPS_PROXY: $HTTPS_PROXY"
echo "  SSL证书: $SSL_CERT_FILE"
echo ""

# 检查mitmproxy是否运行
if ! pgrep -f "mitmweb" > /dev/null; then
    echo "警告: mitmproxy未运行，请先启动:"
    echo "  mitmweb --listen-port 8888 --web-port 8081"
    echo ""
fi

echo "Web抓包界面: http://localhost:8081"
echo "代理端口: 8888"
echo "Go服务端口: 8080"
echo ""

# 加载环境变量并启动
echo "加载.env配置..."
set -a
source .env
set +a

echo "启动Go服务..."
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务
./qwen
