#include <stdio.h>
#include <string.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/ssl.h>
#include <openssl/err.h>

int main() {
    printf("=== OpenSSL 测试程序 ===\n");
    
    // 初始化OpenSSL
    SSL_library_init();
    SSL_load_error_strings();
    OpenSSL_add_all_algorithms();
    
    // 显示版本信息
    printf("OpenSSL 版本: %s\n", OpenSSL_version(OPENSSL_VERSION));
    printf("构建信息: %s\n", OpenSSL_version(OPENSSL_BUILT_ON));
    printf("平台信息: %s\n", OpenSSL_version(OPENSSL_PLATFORM));
    
    // 测试随机数生成
    unsigned char random_bytes[16];
    if (RAND_bytes(random_bytes, sizeof(random_bytes)) == 1) {
        printf("\n✓ 随机数生成测试成功\n");
        printf("随机数据: ");
        for (int i = 0; i < sizeof(random_bytes); i++) {
            printf("%02x", random_bytes[i]);
        }
        printf("\n");
    } else {
        printf("\n✗ 随机数生成测试失败\n");
        return 1;
    }
    
    // 测试哈希算法
    const char *data = "Hello, OpenSSL!";
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    
    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
    if (ctx != NULL) {
        if (EVP_DigestInit_ex(ctx, EVP_sha256(), NULL) == 1 &&
            EVP_DigestUpdate(ctx, data, strlen(data)) == 1 &&
            EVP_DigestFinal_ex(ctx, hash, &hash_len) == 1) {
            
            printf("\n✓ SHA256 哈希测试成功\n");
            printf("原始数据: %s\n", data);
            printf("SHA256 哈希: ");
            for (unsigned int i = 0; i < hash_len; i++) {
                printf("%02x", hash[i]);
            }
            printf("\n");
        } else {
            printf("\n✗ SHA256 哈希测试失败\n");
        }
        EVP_MD_CTX_free(ctx);
    }
    
    // 测试对称加密 (AES)
    const char *plaintext = "This is a test message for AES encryption.";
    unsigned char key[32]; // 256-bit key
    unsigned char iv[16];  // 128-bit IV
    
    // 生成随机密钥和IV
    RAND_bytes(key, sizeof(key));
    RAND_bytes(iv, sizeof(iv));
    
    EVP_CIPHER_CTX *cipher_ctx = EVP_CIPHER_CTX_new();
    if (cipher_ctx != NULL) {
        unsigned char ciphertext[1024];
        int len, ciphertext_len;
        
        // 加密
        if (EVP_EncryptInit_ex(cipher_ctx, EVP_aes_256_cbc(), NULL, key, iv) == 1 &&
            EVP_EncryptUpdate(cipher_ctx, ciphertext, &len, (unsigned char*)plaintext, strlen(plaintext)) == 1) {
            ciphertext_len = len;
            if (EVP_EncryptFinal_ex(cipher_ctx, ciphertext + len, &len) == 1) {
                ciphertext_len += len;
                printf("\n✓ AES-256-CBC 加密测试成功\n");
                printf("原始文本: %s\n", plaintext);
                printf("加密长度: %d 字节\n", ciphertext_len);
            }
        }
        EVP_CIPHER_CTX_free(cipher_ctx);
    }
    
    printf("\n=== OpenSSL 测试完成 ===\n");
    printf("✓ OpenSSL 安装和配置正常！\n");
    
    // 清理
    EVP_cleanup();
    ERR_free_strings();
    
    return 0;
}
