#!/bin/bash

# Qwen Go服务启动脚本
# 作者: AI Assistant
# 日期: 2025-06-17

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo -e "${BLUE}=== Qwen Go服务启动脚本 ===${NC}"
echo -e "${YELLOW}工作目录: $SCRIPT_DIR${NC}"

# 检查必要文件
echo -e "\n${BLUE}1. 检查必要文件...${NC}"

if [ ! -f "qwen" ]; then
    echo -e "${RED}错误: 找不到可执行文件 'qwen'${NC}"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo -e "${RED}错误: 找不到环境配置文件 '.env'${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 可执行文件 qwen 存在${NC}"
echo -e "${GREEN}✓ 环境配置文件 .env 存在${NC}"

# 检查文件权限
if [ ! -x "qwen" ]; then
    echo -e "${YELLOW}警告: qwen 文件没有执行权限，正在添加...${NC}"
    chmod +x qwen
    echo -e "${GREEN}✓ 已添加执行权限${NC}"
fi

# 检查服务依赖
echo -e "\n${BLUE}2. 检查服务依赖...${NC}"

# 检查MySQL
if systemctl is-active --quiet mysql; then
    echo -e "${GREEN}✓ MySQL 服务正在运行${NC}"
else
    echo -e "${YELLOW}警告: MySQL 服务未运行，尝试启动...${NC}"
    sudo systemctl start mysql
    if systemctl is-active --quiet mysql; then
        echo -e "${GREEN}✓ MySQL 服务启动成功${NC}"
    else
        echo -e "${RED}错误: MySQL 服务启动失败${NC}"
        exit 1
    fi
fi

# 检查Redis
if systemctl is-active --quiet redis-server; then
    echo -e "${GREEN}✓ Redis 服务正在运行${NC}"
else
    echo -e "${YELLOW}警告: Redis 服务未运行，尝试启动...${NC}"
    sudo systemctl start redis-server
    if systemctl is-active --quiet redis-server; then
        echo -e "${GREEN}✓ Redis 服务启动成功${NC}"
    else
        echo -e "${RED}错误: Redis 服务启动失败${NC}"
        exit 1
    fi
fi

# 测试数据库连接
echo -e "\n${BLUE}3. 测试数据库连接...${NC}"
source .env

# 测试MySQL连接
mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USERNAME" -p"$MYSQL_PASSWORD" -D"$MYSQL_DATABASE" -e "SELECT 1;" >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ MySQL 连接测试成功${NC}"
else
    echo -e "${RED}错误: MySQL 连接测试失败${NC}"
    echo -e "${YELLOW}请检查 .env 文件中的数据库配置${NC}"
    exit 1
fi

# 测试Redis连接
if [ -z "$REDIS_PASSWORD" ]; then
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping >/dev/null 2>&1
else
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" ping >/dev/null 2>&1
fi

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Redis 连接测试成功${NC}"
else
    echo -e "${RED}错误: Redis 连接测试失败${NC}"
    echo -e "${YELLOW}请检查 .env 文件中的Redis配置${NC}"
    exit 1
fi

# 检查端口占用
echo -e "\n${BLUE}4. 检查端口占用...${NC}"
if lsof -i :8080 >/dev/null 2>&1; then
    echo -e "${YELLOW}警告: 端口 8080 已被占用${NC}"
    echo -e "${YELLOW}正在显示占用进程:${NC}"
    lsof -i :8080
    echo -e "\n${YELLOW}是否要继续启动? (y/N)${NC}"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo -e "${RED}启动已取消${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✓ 端口 8080 可用${NC}"
fi

# 启动服务
echo -e "\n${BLUE}5. 启动 Qwen Go 服务...${NC}"
echo -e "${YELLOW}加载环境变量并启动服务...${NC}"

# 加载环境变量并启动
set -a
source .env
set +a

echo -e "${GREEN}正在启动服务，请稍候...${NC}"
echo -e "${YELLOW}服务将在 http://localhost:8080 上运行${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo -e "\n${BLUE}=== 服务日志 ===${NC}"

# 启动服务
./qwen
