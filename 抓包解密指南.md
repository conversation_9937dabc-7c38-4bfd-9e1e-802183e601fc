# Go服务抓包和HTTPS解密指南

## 已安装的工具

1. **mitmproxy** - HTTP/HTTPS代理和抓包工具
2. **Wireshark** - 网络协议分析器

## 方案1: 使用mitmproxy（推荐）

### 1.1 启动mitmproxy

```bash
# 启动Web界面版本（推荐）
mitmweb --listen-port 8888

# 或启动命令行版本
mitmdump --listen-port 8888

# 或启动交互式版本
mitmproxy --listen-port 8888
```

### 1.2 配置Go服务使用代理

修改您的Go服务，让它通过代理发送请求：

```bash
# 设置环境变量
export HTTP_PROXY=http://localhost:8888
export HTTPS_PROXY=http://localhost:8888

# 启动Go服务
./start_simple.sh
```

### 1.3 安装mitmproxy证书（用于HTTPS解密）

```bash
# 下载证书
curl -o mitmproxy-ca-cert.pem http://mitm.it/cert/pem

# 安装证书到系统
sudo cp mitmproxy-ca-cert.pem /usr/local/share/ca-certificates/mitmproxy.crt
sudo update-ca-certificates

# 或者设置Go程序信任证书
export SSL_CERT_FILE=/path/to/mitmproxy-ca-cert.pem
```

### 1.4 查看抓包结果

- **Web界面**: 访问 http://localhost:8081
- **命令行**: 直接在终端查看
- **保存到文件**: `mitmdump -w capture.mitm`

## 方案2: 使用环境变量和日志

### 2.1 修改Go服务记录请求

在Go代码中添加请求日志：

```go
// 创建HTTP客户端时添加日志
client := &http.Client{
    Transport: &http.Transport{
        // 添加调试传输
    },
}

// 或使用httputil.DumpRequest
reqDump, _ := httputil.DumpRequest(req, true)
log.Printf("Request: %s", reqDump)

respDump, _ := httputil.DumpResponse(resp, true)
log.Printf("Response: %s", respDump)
```

### 2.2 设置Go HTTP调试

```bash
# 启用Go HTTP调试
export GODEBUG=http2debug=1

# 启动服务
./start_simple.sh
```

## 方案3: 使用tcpdump + Wireshark

### 3.1 使用tcpdump抓包

```bash
# 抓取指定端口的流量
sudo tcpdump -i any -w capture.pcap port 8080

# 抓取所有HTTP/HTTPS流量
sudo tcpdump -i any -w capture.pcap 'port 80 or port 443'
```

### 3.2 使用Wireshark分析

```bash
# 启动Wireshark
wireshark capture.pcap

# 或实时抓包
sudo wireshark
```

## 方案4: 使用Go程序内置抓包

### 4.1 创建抓包中间件

创建一个Go中间件来记录所有HTTP请求：

```go
func LoggingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // 记录请求
        body, _ := ioutil.ReadAll(r.Body)
        r.Body = ioutil.NopCloser(bytes.NewBuffer(body))
        
        log.Printf("Request: %s %s", r.Method, r.URL)
        log.Printf("Headers: %v", r.Header)
        log.Printf("Body: %s", body)
        
        // 包装ResponseWriter来记录响应
        recorder := &ResponseRecorder{ResponseWriter: w}
        next.ServeHTTP(recorder, r)
        
        log.Printf("Response Status: %d", recorder.status)
        log.Printf("Response Body: %s", recorder.body)
    })
}
```

## 快速启动脚本

### mitmproxy启动脚本
```bash
#!/bin/bash
echo "启动mitmproxy Web界面..."
echo "Web界面地址: http://localhost:8081"
echo "代理地址: http://localhost:8888"
echo "按Ctrl+C停止"

mitmweb --listen-port 8888 --web-port 8081
```

### 抓包Go服务脚本
```bash
#!/bin/bash
echo "设置代理环境变量..."
export HTTP_PROXY=http://localhost:8888
export HTTPS_PROXY=http://localhost:8888

echo "启动Go服务..."
./start_simple.sh
```

## 使用步骤

1. **启动mitmproxy**:
   ```bash
   mitmweb --listen-port 8888
   ```

2. **配置Go服务使用代理**:
   ```bash
   export HTTP_PROXY=http://localhost:8888
   export HTTPS_PROXY=http://localhost:8888
   ```

3. **启动Go服务**:
   ```bash
   ./start_simple.sh
   ```

4. **访问Web界面查看抓包结果**:
   ```
   http://localhost:8081
   ```

5. **测试API**:
   ```bash
   curl http://localhost:8080/api/test
   ```

## 注意事项

1. **HTTPS解密**: 需要安装mitmproxy证书
2. **代理设置**: 确保Go程序正确使用代理
3. **防火墙**: 确保端口8888和8081没有被阻止
4. **权限**: Wireshark可能需要root权限

## 故障排除

- 如果HTTPS无法解密，检查证书安装
- 如果代理不工作，检查环境变量设置
- 如果端口被占用，更改端口号
- 如果权限不足，使用sudo运行
